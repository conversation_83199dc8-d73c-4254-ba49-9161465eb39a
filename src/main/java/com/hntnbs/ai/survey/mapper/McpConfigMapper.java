package com.hntnbs.ai.survey.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hntnbs.ai.survey.model.McpConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface McpConfigMapper extends BaseMapper<McpConfigEntity> {

    @Select("SELECT * FROM t_e_ai_mcp_config where delete_flag = '0'")
    List<McpConfigEntity> findAll();

    McpConfigEntity findByMcpServerName(String serverName);
}
